import React, { useState, useCallback, useMemo } from 'react';
import { Hash, Lock, Plus, MoreVertical, Settings } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { useTranslations } from '@/lib/i18n/typed-translations';
import { useChatChannels, useChatUtils, useMentionParser } from '@/hooks/chat';
import { CreateChannelModal } from './create-channel-modal';
import { ChannelManagementModal } from './channel-management-modal';
import { ManageParticipantsModal } from './manage-participants-modal';
import { cn } from '@/lib/utils';
import type { ChatChannelResponse, ChatMessageResponse, MentionDto } from '@/lib/types/api';

interface ChannelListProps {
  hubId: number;
  selectedChannelId?: number;
  onChannelSelect: (channelId: number) => void;
  className?: string;
}

export const ChannelList = React.memo<ChannelListProps>(({ hubId, selectedChannelId, onChannelSelect, className }) => {
  const { t, keys } = useTranslations();
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [showManageParticipantsModal, setShowManageParticipantsModal] = useState(false);
  const [selectedChannel, setSelectedChannel] = useState<ChatChannelResponse | null>(null);
  const [openDropdownId, setOpenDropdownId] = useState<number | null>(null);

  const { formatTime } = useChatUtils();
  const { convertMentionsToDisplayFormat } = useMentionParser();

  const {
    data: channelsResponse,
    isLoading,
    error,
  } = useChatChannels(hubId);

  const channels = useMemo(() => channelsResponse?.content || [], [channelsResponse?.content]);

  // Memoized function to get channel icon
  const getChannelIcon = useCallback((scope: string) => {
    switch (scope) {
      case 'general': return <Hash className="h-4 w-4" />;
      case 'custom': return <Lock className="h-4 w-4" />;
      default: return <Hash className="h-4 w-4" />;
    }
  }, []);

  // Memoized event handlers
  const handleChannelSettings = useCallback((channel: ChatChannelResponse) => {
    setSelectedChannel(channel);
    setShowSettingsModal(true);
    setOpenDropdownId(null);
  }, []);

  const handleDropdownToggle = useCallback((channelId: number, isOpen: boolean) => {
    setOpenDropdownId(isOpen ? channelId : null);
  }, []);

  const handleDirectSettingsClick = useCallback((e: React.MouseEvent, channel: ChatChannelResponse) => {
    e.preventDefault();
    e.stopPropagation();
    handleChannelSettings(channel);
  }, [handleChannelSettings]);

  const handleManageParticipants = useCallback(() => {
    setShowSettingsModal(false);
    setShowManageParticipantsModal(true);
  }, []);

  const handleChannelCreated = useCallback((channelId: number) => {
    onChannelSelect(channelId);
  }, [onChannelSelect]);

  const handleChannelDeleted = useCallback(() => {
    // If the deleted channel was selected, clear selection
    if (selectedChannel && selectedChannelId === selectedChannel.id) {
      // Select the first available channel
      const remainingChannels = channels.filter(c => c.id !== selectedChannel.id);
      if (remainingChannels.length > 0) {
        onChannelSelect(remainingChannels[0].id!);
      }
    }
  }, [selectedChannel, selectedChannelId, channels, onChannelSelect]);

  // Memoized utility functions
  const getChannelName = useCallback((channel: { name?: string; scope?: string }) => {
    // Use the channel name from backend, but provide fallbacks for common scopes
    if (channel.name) return channel.name;

    switch (channel.scope) {
      case 'general': return t(keys.collaborationHubs.chat.general);
      case 'custom': return t(keys.collaborationHubs.chat.customChannel);
      default: return t(keys.collaborationHubs.chat.general);
    }
  }, [t, keys]);

  const formatLastMessage = useCallback((message: ChatMessageResponse) => {
    if (!message) return '';

    // If message has attachments, show attachment indicator
    if (message.attachments && message.attachments.length > 0) {
      const attachmentCount = message.attachments.length;
      return `📎 ${attachmentCount} attachment${attachmentCount > 1 ? 's' : ''}`;
    }

    // Convert raw message content (with emails) to display format (with names)
    let content = message.content || '';

    if (message.mentions && message.mentions.length > 0) {
      content = convertMentionsToDisplayFormat(content, message.mentions);
    }

    // Truncate long messages
    return content.length > 50
      ? `${content.substring(0, 50)}...`
      : content;
  }, [convertMentionsToDisplayFormat]);

  if (isLoading) {
    return (
      <div className={cn("space-y-2", className)}>
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="space-y-2">
            <Skeleton className="h-4 w-24" />
            <div className="space-y-1">
              <Skeleton className="h-3 w-full" />
              <Skeleton className="h-3 w-2/3" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn("flex items-center justify-center py-8", className)}>
        <div className="text-center space-y-2">
          <p className="text-sm text-destructive">{t(keys.collaborationHubs.chat.error)}</p>
          <Button variant="outline" size="sm" onClick={() => window.location.reload()}>
            {t(keys.common.retry)}
          </Button>
        </div>
      </div>
    );
  }

  if (channels.length === 0) {
    return (
      <div className={cn("text-center py-8", className)}>
        <p className="text-sm text-muted-foreground">No channels available</p>
      </div>
    );
  }

  return (
    <>
      <ScrollArea className={cn("", className)}>
        <div className="space-y-1">
          {/* Create Channel Button */}
          <Button
            variant="outline"
            className="w-full justify-start mb-3"
            onClick={() => setShowCreateModal(true)}
          >
            <Plus className="h-4 w-4 mr-2" />
            {t(keys.collaborationHubs.chat.createChannel)}
          </Button>

          {/* Channel List */}
          {channels.map((channel) => (
            <div
              key={channel.id}
              className={cn(
                "group relative rounded-lg transition-colors hover:bg-muted/50",
                selectedChannelId === channel.id && "bg-muted"
              )}
            >
              <button
                onClick={() => onChannelSelect(channel.id!)}
                className="w-full text-left p-3 pr-12"
              >
                <div className="flex items-start gap-3">
                  <div className="flex items-center gap-2 min-w-0 flex-1">
                    {getChannelIcon(channel.scope || '')}
                    <div className="min-w-0 flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-sm truncate">
                          {getChannelName(channel)}
                        </span>
                      </div>
                      {channel.last_message && (
                        <p className="text-xs text-muted-foreground line-clamp-1">
                          {formatLastMessage(channel.last_message)}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="flex flex-col items-end gap-1 text-xs text-muted-foreground">
                    {channel.last_activity_at && (
                      <span>{formatTime(channel.last_activity_at)}</span>
                    )}
                    <span>{channel.participant_count || 0}</span>
                  </div>
                </div>
              </button>

              {/* Channel Settings Button - Always Visible */}
              <div className="absolute right-2 top-1/2 transform -translate-y-1/2 z-10">
                <DropdownMenu
                  open={openDropdownId === channel.id}
                  onOpenChange={(isOpen) => handleDropdownToggle(channel.id!, isOpen)}
                >
                  <DropdownMenuTrigger>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 hover:bg-muted-foreground/10"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                      }}
                      onDoubleClick={(e) => handleDirectSettingsClick(e, channel)}
                      title="Channel settings (click for menu, double-click for direct access)"
                    >
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="z-50">
                    <DropdownMenuItem
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleChannelSettings(channel);
                      }}
                    >
                      <Settings className="h-4 w-4 mr-2" />
                      Manage Channel
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>

      {/* Modals */}
      <CreateChannelModal
        hubId={hubId}
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onChannelCreated={handleChannelCreated}
      />

      {selectedChannel && (
        <>
          <ChannelManagementModal
            hubId={hubId}
            channel={selectedChannel}
            isOpen={showSettingsModal}
            onClose={() => setShowSettingsModal(false)}
            onChannelDeleted={handleChannelDeleted}
            onManageParticipants={handleManageParticipants}
          />

          <ManageParticipantsModal
            hubId={hubId}
            channel={selectedChannel}
            isOpen={showManageParticipantsModal}
            onClose={() => setShowManageParticipantsModal(false)}
          />
        </>
      )}
    </>
  );
});

ChannelList.displayName = 'ChannelList';

export function ChannelListSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("space-y-2", className)}>
      {Array.from({ length: 4 }).map((_, i) => (
        <div key={i} className="p-3 space-y-2">
          <div className="flex items-center gap-2">
            <Skeleton className="h-4 w-4" />
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-4 w-6 ml-auto" />
          </div>
          <Skeleton className="h-3 w-full" />
          <div className="flex justify-between">
            <Skeleton className="h-3 w-16" />
            <Skeleton className="h-3 w-8" />
          </div>
        </div>
      ))}
    </div>
  );
}
