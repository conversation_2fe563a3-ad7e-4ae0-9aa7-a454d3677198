import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { MentionInput } from '@/components/ui/mention-input'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { AuthProvider } from '@/contexts/auth-context'

// Mock the hooks
vi.mock('@/hooks/collaboration-hubs', () => ({
  useHubParticipants: vi.fn(() => ({
    data: {
      content: [
        {
          id: 1,
          email: '<EMAIL>',
          name: '<PERSON>',
          role: 'admin',
          isExternal: false
        },
        {
          id: 2,
          email: '<EMAIL>',
          name: '<PERSON>',
          role: 'content_creator',
          isExternal: true
        }
      ]
    },
    isLoading: false
  }))
}))

vi.mock('@/contexts/auth-context', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useCurrentUser: vi.fn(() => ({
    id: 999,
    email: '<EMAIL>',
    name: 'Current User'
  }))
}))

vi.mock('@/lib/i18n/typed-translations', () => ({
  useTranslations: vi.fn(() => ({
    t: (key: string) => key,
    keys: {
      ui: {
        mentionInput: {
          loadingParticipants: 'Loading participants...'
        }
      },
      collaborationHubs: {
        roles: {
          admin: 'Admin',
          content_creator: 'Content Creator',
          reviewer: 'Reviewer',
          reviewer_creator: 'Reviewer & Creator'
        }
      }
    }
  }))
}))

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  })

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        {children}
      </AuthProvider>
    </QueryClientProvider>
  )
}

describe('MentionInput Dual-State System', () => {
  let mockOnChange: ReturnType<typeof vi.fn>

  beforeEach(() => {
    mockOnChange = vi.fn()
  })

  it('should convert email mentions to display format', async () => {
    const emailContent = 'Hello @<EMAIL> and @<EMAIL>!'
    
    render(
      <TestWrapper>
        <MentionInput
          hubId={1}
          value={emailContent}
          onChange={mockOnChange}
        >
          {(props) => (
            <div
              data-testid="mention-input"
              {...props}
            />
          )}
        </MentionInput>
      </TestWrapper>
    )

    const input = screen.getByTestId('mention-input')
    
    // Should display friendly names instead of emails
    await waitFor(() => {
      expect(input.innerHTML).toContain('@John Doe')
      expect(input.innerHTML).toContain('@Jane Smith')
      expect(input.innerHTML).toContain('mention-token')
    })
  })

  it('should convert display mentions back to email format on change', async () => {
    render(
      <TestWrapper>
        <MentionInput
          hubId={1}
          value=""
          onChange={mockOnChange}
        >
          {({ onChange, contentEditable, ...props }) => (
            <div
              data-testid="mention-input"
              contentEditable={contentEditable}
              onInput={(e) => onChange(e)}
              {...props}
            />
          )}
        </MentionInput>
      </TestWrapper>
    )

    const input = screen.getByTestId('mention-input')
    
    // Simulate adding a mention span
    input.innerHTML = 'Hello <span class="mention-token" data-email="<EMAIL>">@John Doe</span>!'
    
    // Trigger input event
    fireEvent.input(input)
    
    // Should call onChange with email format
    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalledWith('Hello @<EMAIL>!')
    })
  })

  it('should handle empty content correctly', () => {
    render(
      <TestWrapper>
        <MentionInput
          hubId={1}
          value=""
          onChange={mockOnChange}
        >
          {(props) => (
            <div
              data-testid="mention-input"
              {...props}
            />
          )}
        </MentionInput>
      </TestWrapper>
    )

    const input = screen.getByTestId('mention-input')
    expect(input.innerHTML).toBe('')
  })

  it('should preserve non-mention content', async () => {
    const content = 'This is regular text without mentions.'
    
    render(
      <TestWrapper>
        <MentionInput
          hubId={1}
          value={content}
          onChange={mockOnChange}
        >
          {(props) => (
            <div
              data-testid="mention-input"
              {...props}
            />
          )}
        </MentionInput>
      </TestWrapper>
    )

    const input = screen.getByTestId('mention-input')
    
    await waitFor(() => {
      expect(input.textContent).toBe(content)
      expect(input.innerHTML).not.toContain('mention-token')
    })
  })
})
