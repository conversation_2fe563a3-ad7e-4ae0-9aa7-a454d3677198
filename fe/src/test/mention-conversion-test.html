<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mention Conversion Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-container {
            border: 1px solid #ccc;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .mention-token {
            color: #3b82f6;
            background-color: rgba(59, 130, 246, 0.1);
            padding: 1px 3px;
            border-radius: 3px;
            font-weight: 500;
            cursor: pointer;
            user-select: none;
            display: inline;
            white-space: nowrap;
        }
        .test-input {
            border: 1px solid #ddd;
            padding: 10px;
            min-height: 100px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2563eb;
        }
    </style>
</head>
<body>
    <h1>Mention Conversion Test</h1>
    <p>This test validates the dual-state mention system conversion from display format to email format.</p>

    <div class="test-container">
        <h2>Test Input</h2>
        <div id="testInput" class="test-input" contenteditable="true">
            test <span class="mention-token" contenteditable="false" data-email="<EMAIL>">@Pavel</span> <span class="mention-token" contenteditable="false" data-email="<EMAIL>">@Pavel Hristov</span> Test <span class="mention-token" contenteditable="false" data-email="<EMAIL>">@<EMAIL></span> test
        </div>
        
        <button onclick="testConversion()">Test Conversion</button>
        <button onclick="resetInput()">Reset Input</button>
        
        <h3>Conversion Result:</h3>
        <div id="result" class="result"></div>
        
        <h3>Expected Result:</h3>
        <div class="result">test @<EMAIL> @<EMAIL> Test @<EMAIL> test</div>
    </div>

    <script>
        // Simulate the convertDisplayMentionsToEmail function from the React component
        function convertDisplayMentionsToEmail(element) {
            // Recursive function to traverse the entire DOM tree
            const traverseNode = (node) => {
                if (node.nodeType === Node.TEXT_NODE) {
                    return node.textContent || '';
                } else if (node.nodeType === Node.ELEMENT_NODE) {
                    const el = node;
                    
                    // Check if this is a mention token
                    if (el.classList.contains('mention-token')) {
                        const email = el.dataset.email;
                        if (email && email.trim()) {
                            // Successfully found mention with email data
                            return `@${email}`;
                        } else {
                            // Mention token without proper email data - this is the bug!
                            console.warn('Mention token found without proper email data:', {
                                element: el,
                                textContent: el.textContent,
                                dataset: el.dataset,
                                className: el.className
                            });
                            
                            // Fallback: try to extract email from text content if it looks like an email
                            const textContent = el.textContent || '';
                            const emailMatch = textContent.match(/@([a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
                            if (emailMatch) {
                                return `@${emailMatch[1]}`;
                            }
                            
                            // If no email found, this mention token is broken - return as display text
                            console.error('Broken mention token - no email data available:', el);
                            return textContent;
                        }
                    } else {
                        // Regular element - recursively process children
                        let result = '';
                        for (const child of el.childNodes) {
                            result += traverseNode(child);
                        }
                        return result;
                    }
                }
                
                return '';
            };

            const result = traverseNode(element);
            
            // Debug logging to help identify conversion issues
            console.log('convertDisplayMentionsToEmail:', {
                input: element.innerHTML,
                output: result,
                mentionTokens: element.querySelectorAll('.mention-token').length
            });
            
            return result;
        }

        function testConversion() {
            const input = document.getElementById('testInput');
            const result = document.getElementById('result');
            
            const converted = convertDisplayMentionsToEmail(input);
            result.textContent = converted;
            
            // Check if conversion is correct
            const expected = 'test @<EMAIL> @<EMAIL> Test @<EMAIL> test';
            if (converted === expected) {
                result.style.backgroundColor = '#d4edda';
                result.style.color = '#155724';
                result.textContent = '✅ PASS: ' + converted;
            } else {
                result.style.backgroundColor = '#f8d7da';
                result.style.color = '#721c24';
                result.textContent = '❌ FAIL: ' + converted + '\n\nExpected: ' + expected;
            }
        }

        function resetInput() {
            const input = document.getElementById('testInput');
            input.innerHTML = 'test <span class="mention-token" contenteditable="false" data-email="<EMAIL>">@Pavel</span> <span class="mention-token" contenteditable="false" data-email="<EMAIL>">@Pavel Hristov</span> Test <span class="mention-token" contenteditable="false" data-email="<EMAIL>">@<EMAIL></span> test';
            
            const result = document.getElementById('result');
            result.textContent = '';
            result.style.backgroundColor = '#f5f5f5';
            result.style.color = 'inherit';
        }

        // Run test automatically on page load
        window.onload = function() {
            testConversion();
        };
    </script>
</body>
</html>
