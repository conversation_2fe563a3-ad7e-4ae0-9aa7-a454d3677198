import '@testing-library/jest-dom'

// Mock window.getSelection for contenteditable tests
Object.defineProperty(window, 'getSelection', {
  writable: true,
  value: () => ({
    rangeCount: 1,
    getRangeAt: () => ({
      startContainer: document.createTextNode(''),
      startOffset: 0,
      endContainer: document.createTextNode(''),
      endOffset: 0,
      collapse: () => {},
      setStartAfter: () => {},
    }),
    removeAllRanges: () => {},
    addRange: () => {},
  }),
})

// Mock document.createRange for contenteditable tests
Object.defineProperty(document, 'createRange', {
  writable: true,
  value: () => ({
    setStartAfter: () => {},
    collapse: () => {},
    startContainer: document.createTextNode(''),
    startOffset: 0,
    endContainer: document.createTextNode(''),
    endOffset: 0,
  }),
})

// Mock document.createTreeWalker for cursor position calculations
Object.defineProperty(document, 'createTreeWalker', {
  writable: true,
  value: () => ({
    nextNode: () => null,
  }),
})
